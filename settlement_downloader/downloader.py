import logging
from datetime import datetime
from typing import List

from db_manager.base_mysql import MySQLDB
from .base import SettlementPlatform
from .platform_shop_config import PlatformShopConfig

logger = logging.getLogger(__name__)


class SettlementDownloader:
    """结算单下载器，管理多个平台"""

    def __init__(self, db: MySQLDB, platforms: List[SettlementPlatform]):
        self.db = db
        self.platforms = platforms

    def download_settlements(
            self,
            start_date: datetime,
            max_attempts: int = 1
    ) -> None:
        """下载指定时间范围内的结算单"""

        for platform in self.platforms:
            logger.info(f"平台: {platform.platform_type()} 开始处理------\n")
            try:
                self._process_platform(platform, start_date, max_attempts)
            except Exception as e:
                logger.error(f"Error 平台 {platform.platform_type} : {str(e)}")

    def _process_platform(
            self,
            platform: SettlementPlatform,
            start_date: datetime,
            max_attempts: int
    ) -> None:
        """处理单个平台的下载逻辑"""
        # 拿到所有店铺
        shops = PlatformShopConfig().get_shops_by_platform(platform.platform_type())
        for shop in shops:
            logger.info(f"店铺:[{shop.shop_name}] 开始处理")
            platform.run(shop, self.db, start_date)
            logger.info(f"店铺:[{shop.shop_name}] 处理完成\n")
