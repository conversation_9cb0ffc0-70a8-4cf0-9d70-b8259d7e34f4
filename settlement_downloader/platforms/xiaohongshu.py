from datetime import datetime
from typing import Dict, Any

import shop_manager
from db_manager import TableFields
from db_manager.base_mysql import MySQLDB
from settlement_downloader.base import SettlementPlatform
import requests
import logging
import pandas as pd
import io

from settlement_downloader.exceptions import PlatformRunError
from settlement_downloader.platform_shop_config import Shop
from settlement_downloader.utils import get_first_day_of_month_timestamp, has_shop_existing_data

logger = logging.getLogger(__name__)


class Xiaohongshu(SettlementPlatform):

    def platform_type(self) -> str:
        return shop_manager.PlatformType.XIAOHONGSHU.value

    def run(self, shop: Shop, db: MySQLDB, start_date: datetime):
        # 查询当月是否有数据
        first_day_of_month_timestamp = get_first_day_of_month_timestamp(start_date)
        if has_shop_existing_data(db, shop.shop_id, first_day_of_month_timestamp):
            logger.info(f"店铺 {shop.shop_name} 的当月数据已存在，跳过处理")
            return None

        data = []

        # 请求接口拿到下载地址
        url = 'https://callback.vinehoo.com/xiaohongshu-sync/xiaohongshu/v3/bill_details'
        post_data = {
            'shop_id': shop.shop_id,
            'month': start_date.strftime("%Y-%m")
        }

        resp = requests.post(url, post_data).json()
        if resp.get('error_code', -1) != 0:
            raise PlatformRunError(f"响应:{resp}")
        # 示例，返回一个excel：https://finance-private.xiaohongshu.com/酒云网的店S202503010087459202503011740764150136.xlsx?e=1742459288&token=edMBpdzSWI2pH13onoAIcpYw7pSU2tplRrPTySQO:NygF6G0NJjB41JB0gGgq-QDX9_I=
        download_url = resp.get('data', {}).get('downloadUrl', '')
        if not download_url:
            raise PlatformRunError(f"下载地址为空：响应:{resp}")

        # 下载Excel文件
        excel_response = requests.get(download_url)
        if excel_response.status_code != 200:
            raise PlatformRunError(
                f"下载Excel文件失败: status_code={excel_response.status_code}")

        # 读取Excel文件内容
        try:
            # 读取所有sheet
            excel_file = pd.ExcelFile(io.BytesIO(excel_response.content))

            for sheet in ["订单销售", "订单退款"]:
                if sheet in excel_file.sheet_names:
                    sheet_df = pd.read_excel(excel_file, sheet_name=sheet)
                    # 处理每一行数据
                    for _, row in sheet_df.iterrows():
                        settlement_type = "收入"
                        if sheet == "订单退款":
                            settlement_type = "退款"

                        # 将 pandas Timestamp 转换为 Python datetime
                        settlement_time = pd.to_datetime(
                            row['结算时间']).to_pydatetime()

                        data.append({
                            TableFields.MAIN_ORDER_ID: str(row['订单号']),
                            TableFields.ORDER_ID: str(row['订单号']),
                            TableFields.PLATFORM_ID: shop.shop_id,
                            TableFields.PLATFORM_NAME: shop.shop_name,
                            TableFields.PLATFORM_TYPE: self.platform_type(),
                            TableFields.SETTLEMENT_TIME: settlement_time,  # 使用转换后的datetime
                            TableFields.SETTLEMENT_AMOUNT: float(row['计佣基数']),
                            TableFields.SETTLEMENT_TYPE: settlement_type,
                            TableFields.SETTLEMENT_CHANNEL: "",
                        })

        except Exception as e:
            raise PlatformRunError(f"处理Excel文件失败: {str(e)}")
        if len(data) > 0:
            if not db.insert_batch_data(data):
                raise PlatformRunError(f"保存数据失败")

        return data
