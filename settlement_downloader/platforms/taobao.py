from datetime import datetime

import shop_manager
from db_manager.base_mysql import MySQLDB
from settlement_downloader.base import SettlementPlatform
import requests
import logging
import pandas as pd
import io

from settlement_downloader.exceptions import PlatformRunError
from settlement_downloader.platform_shop_config import Shop
from settlement_downloader.utils import get_first_day_of_month_timestamp, datetime_to_timestamp, has_shop_existing_data, \
    get_last_day_of_month_timestamp

logger = logging.getLogger(__name__)


class Taobao(SettlementPlatform):

    def platform_type(self) -> str:
        return shop_manager.PlatformType.TAOBAO.value

    def run(self, shop: Shop, db: MySQLDB):
        # 处理当月数据
        month = datetime.now().strftime('%Y%m')
        systemTaskCt = db.count("vh_tripartite_settlement_task", where="shop_id = %s and month = %s and type = 1",
                                args=(shop.shop_id, month))
        if systemTaskCt == 0:
            self._month_settlement(shop, db, month)

        # 处理人工添加的
        tasks = db.find_all('vh_tripartite_settlement_task', where="shop_id = %s and type = 2 and status = 1",
                            args=(shop.shop_id,), )
        if len(tasks) > 0:
            for task in tasks:
                self._month_settlement(shop, db, task["month"], task["id"], task['is_delete'] == 1)

    def _month_settlement(self, shop: Shop, db: MySQLDB, month: str, task_id: int = 0, is_delete: bool = False) -> bool:
        data = []
        try:
            # 文件格式只会是：csv|xls|xlsx
            file_urls = shop.get_oss_attachment()
            # 要跑的月份数据，会自动走oss去获取上传的结算数据，如果没传就不会跑
            if file_urls:
                for file_url in file_urls:
                    parts = file_url.split('/')[2].split('-')
                    if parts[2].find(month) != -1:
                        # 下载并处理文件
                        df = self._download_and_read_file(file_url)
                        # 处理数据
                        file_data = self._process_file_data(df, parts, shop)
                        data.extend(file_data)

            if data:
                with db.transaction() as conn:
                    if task_id == 0:
                        db.insert("vh_tripartite_settlement_task",
                                  {"shop_id": shop.shop_id, "month": month, "type": 1, "status": 2,
                                   "complete_time": datetime.now()}, conn=conn)
                    else:
                        if is_delete:
                            dt = datetime.strptime(month, "%Y%m")
                            db.delete("vh_tripartite_settlement",
                                      "shop_id = %s and create_time >= %s and create_time <= %s",
                                      (shop.shop_id, get_first_day_of_month_timestamp(dt),
                                       get_last_day_of_month_timestamp(dt)), conn=conn, )
                        db.update("vh_tripartite_settlement_task",
                                  {"status": "2", "complete_time": datetime.now()},
                                  'id = %s', (task_id,),
                                  conn=conn)
                    db.batch_insert("vh_tripartite_settlement", data, conn=conn)
        except Exception as e:
            logger.error(f"处理Excel文件失败: {str(e)}")
            return False
        return True

    def _download_and_read_file(self, file_url: str) -> pd.DataFrame:
        """下载并读取文件内容"""
        # 下载文件
        excel_response = requests.get("https://images.vinehoo.com/" + file_url)
        if excel_response.status_code != 200:
            raise PlatformRunError(
                f"下载Excel文件失败: status_code={excel_response.status_code}")

        # 读取文件内容
        if file_url.endswith('.csv'):
            try:
                return pd.read_csv(io.BytesIO(excel_response.content), comment='#', encoding='GBK')
            except Exception as e:
                raise PlatformRunError(f"读取CSV文件失败: {str(e)}")
        else:
            df = pd.read_excel(io.BytesIO(excel_response.content),
                               dtype={'子订单id': str, '主订单id': str})
            df['子订单id'] = df['子订单id'].fillna('')
            df['主订单id'] = df['主订单id'].fillna('')
            return df

    def _process_file_data(self, df: pd.DataFrame, parts: list, shop: Shop) -> list:
        """处理文件数据"""
        data = []

        for _, row in df.iterrows():
            if parts[0] == "支付宝":
                record = self._process_alipay_row(row)
            else:
                record = self._process_wx_row(row)

            # 添加公共字段
            record.update({
                'platform': self.platform_type(),
                'shop_id': shop.shop_id,
                'shop_name': shop.shop_name,
                'entity': shop.entity,
            })

            data.append(record)

        return data

    def _process_alipay_row(self, row) -> dict:
        """处理支付宝数据行"""
        business = row['业务类型']
        amount = float(row['收入金额（+元）']) + float(row['支出金额（-元）'])
        create_time = datetime_to_timestamp(row['发生时间'])
        main_order_no, sub_order_no = '', ''

        if business in ['交易分账', '收费']:
            main_order_no = str(row['商户订单号'][5:])
        elif business == '交易付款':
            main_order_no = str(row['商户订单号'][5:])
            business = '收入'
        elif business == '交易退款':
            # 退款时商户订单号是子订单号，主订单在备注里面(例如：售后退款-2025040622001149351447596955-T200P2521204791757348550)
            sub_order_no = str(row['商户订单号'][5:])
            main_order_no = row['备注'].split('-')[2][5:]
            business = '退款'

        return {
            'main_order_no': main_order_no.strip(),
            'sub_order_no': sub_order_no.strip(),
            'business': business,
            'amount': amount,
            'create_time': create_time,
        }

    def _process_wx_row(self, row) -> dict:
        """处理微信数据行"""
        business = row['入帐类型']
        main_order_no = str(row['主订单id'])
        sub_order_no = str(row['子订单id'])
        amount = float(row['收入金额(元)']) - float(row['支出金额(元)'])
        create_time = datetime_to_timestamp(row['入帐时间'])

        if business == '交易收款':
            business = '收入'
        elif business == '交易退款(售后)':
            business = '退款'

        return {
            'main_order_no': main_order_no.strip(),
            'sub_order_no': sub_order_no.strip(),
            'business': business,
            'amount': amount,
            'create_time': create_time,
        }
